/**
 * 全局自动技术债工作流脚本
 * 功能：
 * 1. 通过Templater Startup Templates自动初始化
 * 2. 监听所有技术债文件的修改
 * 3. 自动检测表格中状态为"进行中"的行动
 * 4. 自动在Plan文件中创建对应任务（不添加技术债前缀）
 * 5. 仅处理被修改的表格行内容
 */

module.exports = async (tp) => {
  // 避免重复初始化
  if (window.globalTechDebtWorkflowInitialized) {
    console.log("全局技术债工作流已经运行中");
    return;
  }

  console.log("初始化全局技术债工作流...");

  const vault = app.vault;

  // 存储文件的上一次内容，用于比较变化
  window.techDebtFileContents = new Map();

  // 创建全局监听器
  const globalListener = async (file) => {
    // 只处理技术债文件
    if (
      file.path.includes("技术债") &&
      file.path.match(/td-\d{8}-\d{2}\.md$/)
    ) {
      console.log("检测到技术债文件修改:", file.path);

      // 延迟执行，确保文件保存完成
      setTimeout(async () => {
        await processTechDebtFile(file);
      }, 1000);
    }
  };

  // 注册全局监听器
  vault.on("modify", globalListener);

  // 保存监听器引用
  window.globalTechDebtListener = globalListener;
  window.globalTechDebtWorkflowInitialized = true;
  console.log("全局技术债工作流已启用");
};

// 处理技术债文件
async function processTechDebtFile(techDebtFile) {
  try {
    console.log("开始处理技术债文件:", techDebtFile.path);

    // 提取项目名称
    const pathParts = techDebtFile.path.split("/");
    let projectName = null;
    for (let i = 0; i < pathParts.length; i++) {
      if (pathParts[i] === "3-过程资产" && i + 1 < pathParts.length) {
        projectName = pathParts[i + 1];
        break;
      }
    }

    if (!projectName) {
      console.log("无法提取项目名称");
      return;
    }

    // 读取技术债文件内容
    const techDebtContent = await app.vault.read(techDebtFile);

    // 获取上一次的文件内容进行比较
    const previousContent =
      window.techDebtFileContents.get(techDebtFile.path) || "";
    window.techDebtFileContents.set(techDebtFile.path, techDebtContent);

    // 解析偿还计划表格
    const tableRegex =
      /# 2\. 偿还计划[\s\S]*?\n(\|[^\n]*\|\s*\n\|[^\n]*\|\s*\n(?:\|[^\n]*\|\s*\n)*)/;
    const tableMatch = techDebtContent.match(tableRegex);

    if (!tableMatch) {
      console.log("未找到偿还计划表格");
      return;
    }

    const tableContent = tableMatch[1];

    // 解析表格行
    const rows = tableContent
      .split("\n")
      .filter((row) => row.trim() && !row.includes("---"));
    const headerRow = rows[0];
    const dataRows = rows.slice(1);

    // 解析表头
    const headers = headerRow.split("|").map((h) => h.trim());
    while (headers.length > 0 && headers[0] === "") headers.shift();
    while (headers.length > 0 && headers[headers.length - 1] === "")
      headers.pop();

    const actionIndex = headers.findIndex((h) => h.includes("行动"));
    const statusIndex = headers.findIndex((h) => h.includes("状态"));

    if (actionIndex === -1 || statusIndex === -1) {
      console.log("表格格式不正确，未找到'行动'或'状态'列");
      return;
    }

    // 获取新修改的行动项
    const newInProgressActions = getModifiedActions(
      previousContent,
      techDebtContent,
      actionIndex,
      statusIndex
    );

    if (newInProgressActions.length === 0) {
      console.log("未找到新修改的进行中行动项");
      return;
    }

    console.log("找到新修改的进行中行动:", newInProgressActions);

    // 查找对应的Plan文件
    const planFileName = findCurrentWeekPlanFile(projectName);
    if (!planFileName) {
      console.log("未找到当前周的Plan文件");
      return;
    }

    const planFile = app.vault.getAbstractFileByPath(planFileName);
    if (!planFile) {
      console.log(`Plan文件不存在: ${planFileName}`);
      return;
    }

    // 检查并创建任务
    const planContent = await app.vault.read(planFile);
    let updatedPlanContent = planContent;
    let newTasksCount = 0;

    for (const action of newInProgressActions) {
      // 直接使用行动内容作为任务文本，不添加技术债前缀
      const taskText = `- [ ] ${action}`;

      if (!planContent.includes(taskText)) {
        updatedPlanContent += "\n" + taskText;
        newTasksCount++;
        console.log("自动添加任务:", taskText);
      }
    }

    // 更新Plan文件
    if (newTasksCount > 0) {
      await app.vault.modify(planFile, updatedPlanContent);
      // 显示通知（可选，避免过于频繁的通知）
      if (newTasksCount <= 3) {
        new Notice(`自动创建了 ${newTasksCount} 个任务`);
      }
    }
  } catch (error) {
    console.error("处理技术债文件出错:", error);
  }
}

// 辅助函数：获取修改的行动项
function getModifiedActions(
  previousContent,
  currentContent,
  actionIndex,
  statusIndex
) {
  try {
    // 如果没有之前的内容，返回空数组（避免首次加载时创建所有任务）
    if (!previousContent || previousContent.trim() === "") {
      console.log("没有之前的内容，跳过任务创建");
      return [];
    }

    // 解析之前和当前的表格行数据
    const previousRows = parseTableRows(
      previousContent,
      actionIndex,
      statusIndex
    );
    const currentRows = parseTableRows(
      currentContent,
      actionIndex,
      statusIndex
    );

    // 找出新增的或被修改的进行中行动项
    const modifiedActions = [];

    for (const currentRow of currentRows) {
      if (currentRow.status === "进行中") {
        // 查找是否存在相同位置的之前行
        const currentIndex = currentRows.indexOf(currentRow);
        const previousRow = previousRows[currentIndex];

        // 如果是新行，或者行动内容发生了变化，或者状态从非进行中变为进行中
        if (
          !previousRow ||
          previousRow.action !== currentRow.action ||
          previousRow.status !== "进行中"
        ) {
          modifiedActions.push(currentRow.action);
        }
      }
    }

    return modifiedActions;
  } catch (error) {
    console.error("比较文件内容出错:", error);
    // 如果比较出错，返回空数组避免误创建任务
    return [];
  }
}

// 辅助函数：解析表格行数据（包含行动和状态）
function parseTableRows(content, actionIndex, statusIndex) {
  try {
    const tableRegex =
      /# 2\. 偿还计划[\s\S]*?\n(\|[^\n]*\|\s*\n\|[^\n]*\|\s*\n(?:\|[^\n]*\|\s*\n)*)/;
    const tableMatch = content.match(tableRegex);

    if (!tableMatch) {
      return [];
    }

    const tableContent = tableMatch[1];
    const rows = tableContent
      .split("\n")
      .filter((row) => row.trim() && !row.includes("---"));
    const dataRows = rows.slice(1); // 跳过表头

    const rowData = [];
    for (const row of dataRows) {
      if (!row.trim()) continue;

      const cells = row.split("|").map((c) => c.trim());
      while (cells.length > 0 && cells[0] === "") cells.shift();
      while (cells.length > 0 && cells[cells.length - 1] === "") cells.pop();

      if (cells.length > Math.max(actionIndex, statusIndex)) {
        const action = cells[actionIndex] || "";
        const status = cells[statusIndex] || "";

        if (action.trim()) {
          rowData.push({
            action: action.trim(),
            status: status.trim(),
          });
        }
      }
    }

    return rowData;
  } catch (error) {
    console.error("解析表格行数据出错:", error);
    return [];
  }
}

// 辅助函数：查找当前周的Plan文件
function findCurrentWeekPlanFile(projectName) {
  try {
    const today = new Date();
    const year = today.getFullYear();

    // 计算当前是第几周
    const startOfYear = new Date(year, 0, 1);
    const pastDaysOfYear = (today - startOfYear) / 86400000;
    const weekNumber = Math.ceil(
      (pastDaysOfYear + startOfYear.getDay() + 1) / 7
    );

    const planFileName = `2-项目/${projectName}/1-每周规划/Plan-${year}-WK${weekNumber
      .toString()
      .padStart(2, "0")}.md`;

    console.log("查找Plan文件:", planFileName);
    return planFileName;
  } catch (error) {
    console.error("查找Plan文件出错:", error);
    return null;
  }
}

// 停止全局工作流的函数（调试用）
window.stopGlobalTechDebtWorkflow = function () {
  if (window.globalTechDebtListener) {
    app.vault.off("modify", window.globalTechDebtListener);
    window.globalTechDebtListener = null;
    window.globalTechDebtWorkflowInitialized = false;
    console.log("全局技术债工作流已停止");
    new Notice("全局技术债工作流已停止");
  }
};
