
```dataviewjs
const projectName = dv.current().file.path.split("/")[1];
const folderPath = `3-过程资产/${projectName}/阻碍`;
const files = dv.pages(`"${folderPath}"`)
    .filter(p => p.file.name !== "dashboard" && p.file.name !== "测试")
    .sort(p => {
        const priority = p.priority; // 使用 frontmatter 中的 priority 字段
        // 转换为数字排序（处理未设置/非数字值）
        return priority ? Number(priority) : -Infinity; 
    }, "desc"); // 降序排序

dv.table(
    ["序号", "文件链接", "优先级", "别名"],
    files.map((p, index) => {
        const aliases = p.aliases 
            ? (Array.isArray(p.aliases) ? p.aliases.join(", ") : p.aliases)
            : "-";
        return [
            index + 1,
            p.file.link,
            p.priority || "未设置", // 显示原始值
            aliases,
        ];
    })
);
```
