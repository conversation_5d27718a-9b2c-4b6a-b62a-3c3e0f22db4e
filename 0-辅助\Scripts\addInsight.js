/**
 * 快速记录突发灵感
 * 功能：
 * 1. 弹出选择弹窗让用户选择"洞见"、"知识"或"打开文档"
 * 2. 弹出输入弹窗让用户输入内容
 * 3. 洞见：添加到「改进待办事项」"缓存区"作为列表项
 * 4. 知识：自动打开「效率工具箱」文件
 * 5. 打开文档：直接在新标签页打开目标文件
 */

module.exports = async (tp) => {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个项目文件");
      return null;
    }

    // 从路径中提取项目名称
    const projectName = activeFile.path.split("/")[1];
    const targetFilePath = `2-项目/${projectName}/改进待办事项.md`;

    // 步骤1：弹出选择弹窗，让用户选择类型
    const taskType = await tp.system.suggester(
      ["【1】洞见/发现", "【2】效率工具箱", "【3】改进待办事项"],
      ["洞见", "效率工具箱", "改进待办事项"]
    );

    // 如果用户取消了选择，则退出脚本
    if (!taskType) {
      new Notice("已取消添加动作");
      return null;
    }

    // 步骤2：如果是效率工具箱类型，直接打开效率工具箱
    if (taskType === "效率工具箱") {
      await openTargetFile(projectName, "效率工具箱");
      return true;
    }

    // 步骤2.1：如果是改进待办事项类型，直接打开改进待办事项文件
    if (taskType === "改进待办事项") {
      await openTargetFile(projectName, "改进待办事项");
      return true;
    }

    // 步骤3：对于洞见类型，弹出输入弹窗
    const taskContent = await tp.system.prompt(`请输入${taskType}内容:`);

    // 如果用户取消了输入或输入为空，则退出脚本
    if (!taskContent || taskContent.trim() === "") {
      new Notice("内容不能为空，已取消添加");
      return null;
    }

    // 对于洞见类型，处理文件操作
    // 获取目标文件，如果不存在则根据模板创建
    let targetFile = app.vault.getAbstractFileByPath(targetFilePath);
    if (!targetFile) {
      await createFileFromTemplate(targetFilePath, "TP-Project-改进事项.md");
      targetFile = app.vault.getAbstractFileByPath(targetFilePath);
      if (!targetFile) {
        new Notice(`创建目标文件失败: ${targetFilePath}`);
        return null;
      }
    }

    // 读取文件内容
    const fileContent = await app.vault.read(targetFile);

    // 查找"缓存区"标题
    const headingRegex = new RegExp(
      `^#+\\s.*缓存区.*\\n([\\s\\S]*?)(?=\\n#+|$)`,
      "im"
    );
    const match = fileContent.match(headingRegex);

    if (!match) {
      new Notice(`未找到"缓存区"标题`);
      return null;
    }

    // 在标题下添加新条目
    const headingContent = match[0];
    const headingEndPos =
      fileContent.indexOf(headingContent) + headingContent.length;

    // 创建新条目（洞见类型添加为列表）
    let newTask = `- ${taskContent.trim()}`;
    if (!headingContent.endsWith("\n\n")) {
      newTask = "\n" + newTask;
    }

    // 更新文件内容
    const newContent =
      fileContent.substring(0, headingEndPos) +
      newTask +
      fileContent.substring(headingEndPos);

    // 写入文件
    await app.vault.modify(targetFile, newContent);

    // 显示成功通知
    new Notice(
      `已添加 ${taskType}: ${taskContent.substring(0, 20)}${
        taskContent.length > 20 ? "..." : ""
      }`
    );

    return true;
  } catch (error) {
    console.error("添加灵感时出错:", error);
    new Notice(`添加失败: ${error.message}`);
    return null;
  }

  // 打开目标文件的辅助函数
  async function openTargetFile(projectName, fileType) {
    try {
      let filePath = "";
      let fileTitle = "";
      let defaultContent = "";
      let templateName = "";

      // 根据文件类型设置相应的路径和标题
      if (fileType === "效率工具箱") {
        filePath = `3-过程资产/${projectName}/其他/效率工具箱.md`;
        fileTitle = "效率工具箱";
        defaultContent = "# 效率工具箱\n\n";
      } else if (fileType === "改进待办事项") {
        filePath = `2-项目/${projectName}/改进待办事项.md`;
        fileTitle = "改进待办事项";
        templateName = "TP-Project-改进事项.md";
      } else {
        new Notice(`不支持的文件类型: ${fileType}`);
        return false;
      }

      // 获取目标文件
      let targetFile = app.vault.getAbstractFileByPath(filePath);

      // 如果文件不存在，创建文件
      if (!targetFile) {
        // 确保目录存在
        const dirPath = filePath.substring(0, filePath.lastIndexOf("/"));
        const dirExists = app.vault.getAbstractFileByPath(dirPath);
        if (!dirExists) {
          await createDirectoryStructure(dirPath);
        }

        // 如果有模板名称，则使用模板创建文件
        if (templateName) {
          await createFileFromTemplate(filePath, templateName);
        } else {
          // 否则使用默认内容创建文件
          await app.vault.create(filePath, defaultContent);
        }

        targetFile = app.vault.getAbstractFileByPath(filePath);
        if (!targetFile) {
          new Notice(`创建文件失败: ${filePath}`);
          return false;
        }
      }

      // 在新标签页打开文件
      const leaf = app.workspace.getLeaf("tab");
      await leaf.openFile(targetFile);

      new Notice(`已打开${fileTitle}`);
      return true;
    } catch (error) {
      console.error(`打开${fileType}时出错:`, error);
      new Notice(`打开${fileType}失败: ${error.message}`);
      return false;
    }
  }

  // 根据模板创建文件的辅助函数
  async function createFileFromTemplate(targetPath, templateName) {
    try {
      const templatePath = `0-辅助/Templates/${templateName}`;
      const templateFile = app.vault.getAbstractFileByPath(templatePath);

      if (!templateFile) {
        new Notice(`模板文件不存在: ${templatePath}`);
        return false;
      }

      // 读取模板内容
      const templateContent = await app.vault.read(templateFile);

      // 创建目标文件
      await app.vault.create(targetPath, templateContent);

      return true;
    } catch (error) {
      console.error("根据模板创建文件时出错:", error);
      new Notice(`创建文件失败: ${error.message}`);
      return false;
    }
  }

  // 创建目录结构的辅助函数
  async function createDirectoryStructure(dirPath) {
    try {
      const pathParts = dirPath.split("/");
      let currentPath = "";

      for (const part of pathParts) {
        if (part) {
          currentPath = currentPath ? `${currentPath}/${part}` : part;
          const exists = app.vault.getAbstractFileByPath(currentPath);
          if (!exists) {
            await app.vault.createFolder(currentPath);
          }
        }
      }
    } catch (error) {
      console.error("创建目录结构时出错:", error);
      throw error;
    }
  }
};
