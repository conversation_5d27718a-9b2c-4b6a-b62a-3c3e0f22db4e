---
aliases:
  - 「每周回顾」阻碍优先级看板排序失效
created_Date: 2025-07-19
status: 进行中
count: 1
timeCost: 2
drain_val: 5
relation:
  - "[[Review-2025-WK29]]"
cssclasses:
  - c3
type: 认知断层
priority: 3
---
# 1. 基础信息

- 现象描述：「每周回顾」阶段无法对阻碍进行优先级排序、筛选高价值阻碍
- 直接影响：无法明确周回顾的具体切入口
- 衍生影响：导致周回顾工作中断

# 2. 临时方案

| 生效时间                | 目标                   | 临时方案描述                                                                                | 决策依据                                           | 已知风险与局限           | 状态跟踪 | 债务等级 | 知识缺口 |
| ------------------- | -------------------- | ------------------------------------------------------------------------------------- | ---------------------------------------------- | ----------------- | ---- | ---- | ---- |
| 2025-07-19 10:15:03 | 寻找筛选高价值阻碍且适合周回顾的临时方法 | 1、为每个阻碍添加临时的“优先级”字段<br>2、整体评估阻碍的严重程度，用1-10分进行评估，分值越大优先级越高<br>2、创建临时查询代码，按照“优先级”字段进行排序 | 凭感觉已经无法进行排序，根据最新标准简化模型可以降低操作难度，节省时间，也兼容了一定的合理性 | 1、增加了后续阻碍标准化的执行难度 | 生效中  | ★    |      |
# 3. 根因分析

- 核心问题是什么？ --> 

# 5. 最终方案（可选）

| 生效时间 | 根本原因 | 方案描述 | 决策依据 | 已知风险与局限 |
| ---- | ---- | ---- | ---- | ------- |
|      |      |      |      |         |
