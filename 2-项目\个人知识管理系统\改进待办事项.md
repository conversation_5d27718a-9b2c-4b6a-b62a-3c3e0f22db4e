# 1. 缓存区

- 在`0-辅助`文件创建版本记录文档
- 在「每周回顾」“改进项梳理”显示“行动项”列表便于进行整体评估排序
- 

# 2. 留观区

- 灵感VS下一步思考VS下一步工作？
- 「每周评审」“KR进度”关键字段“关联KR”链接非动态链接
- 在记录障碍日志时，是否所有的障碍都需要记录
- 使用脚本创建阻碍时报错：Templater Error:Template parsing error,aborting. check console for more information
- 根据「每日执行」模板新建文件时，提示：Templater Error: No active editor, can't append templates
- ”改善代办“与”闪念“管理的方式十分相似，但是发现的BUG或者其他类型的任务应该怎么办
- 使用JS脚本创建阻碍时，文件名称对应的时间与笔记属性的创建时间不一致
- 在查看“任务安排”中的任务信息时，是否需要显示每条任务的源文件链接？
- 在执行每日任务期间，若当日任务为持续性任务或一日不能完成的任务，那么应该如何记录和安排
- 在obsidian中新建、重新打开文件或重启软件时，鼠标光标的默认位置会影响文件的查看（特别是开头为代码块），不利于查看和编辑文档
- 在excallidraw绘图时，默认的字号类型不能覆盖全部的使用场景，导致部分场景下的图形布局杂乱或畸变
- 在项目推进（迭代）期间，KR进度中已关联的“证据”被优化或删除，原始文件是否需要同时删除？
- 在进行知识版本管理时，通过技术手段使知识组件笔记属性[update_Date]在修改完文件后自动更新，可以直观了解最新的修改状态

# 3. 行动区

- [x] 编辑一个在「每日执行」中添加输出时，自动添加标签（目标、改善、技术债）的自定义脚本 🔼 ✅ 2025-07-21
- [ ] 删除“关键字段提示信息表”将关键内容移动至“效率工具” 🔽
- [ ] 编写自动创建「输出」、自动建立「输出」双链的JS脚本 🔽
- [x] 修复「每周评审」改善回顾数据统计不完整问题 🔼 ✅ 2025-07-21
- [ ] 为「dashboard」添加“search”属性（快速模糊搜索） 🔼
- [x] 编写自动创建「每周计划」、「每日执行」、「每周评审」、「每周回顾」文件的JS脚本 🔽 ✅ 2025-07-21
- [ ] 优化阻碍创建JS脚本（笔记属性自动创建关联阻碍链接） 
- [ ] 优化「dataview-table-style.css」功能（表格宽度自适应） 
- [ ] 为「dashboard」添加“折线图”（添加周维度新增阻碍情况） 
- [x] 优化「障碍日志」模板（新增“发生次数”属性） ⏫ ✅ 2025-07-21
- [x] 按照最新的「障碍日志模板」重新梳理记录的阻碍内容 ⛔ 0mmg6a 🔼 ✅ 2025-07-21
- [x] 结合[[blocker-20250630-01]]重新优化「障碍日志」模板 🆔 0mmg6a ⏫ ✅ 2025-07-21
- [x] 优化并限制「每周回顾」“类型分析”显示结果数量 🔼 ✅ 2025-07-21
- [x] 修复「每周评审」”改善“看板任务统计失效问题 🔼 ✅ 2025-07-21
- [x] 修复阻碍创建脚本按下”ESC“无法取消创建动作的BUG 🔼 ✅ 2025-07-21
- [x] 重新梳理所有阻碍描述 🔼 ✅ 2025-07-21
- [ ] 将文件[[Do-2025-06-15]]、[[Do-2025-06-14]]、[[Do-2025-06-12]]、[[Do-2025-06-08]]中的技术债或阻碍按照最新的管理方式存储，并删除文件中冗余的内容 ⛔ 0mmg6a 🔼
- [x] 统一项目文件夹下子文件夹的名称，并同步调整组件的查询代码 🔽 ✅ 2025-07-21

