# 障碍日志管理脚本使用说明

## 功能概述

这个脚本提供了完整的障碍日志管理功能，包括创建和定位两大核心功能：

### 🆕 新建阻碍功能
1. **自动提取项目名称**：从当前活动文件的路径中自动提取项目名称
2. **智能文件命名**：按照 `blocker-YYYYMMDD-序号.md` 格式命名
3. **自动目录管理**：在 `3-过程资产/项目名称/阻碍` 目录中创建文件
4. **模板应用**：使用预定义的障碍日志模板
5. **别名支持**：支持为障碍日志设置自定义别名
6. **自动打开**：创建后自动在新标签页打开文件

### 🎯 定位阻碍功能
1. **Dashboard 管理**：自动创建或打开项目的阻碍管理面板
2. **模板自动化**：如果 dashboard.md 不存在，自动使用模板创建
3. **快速访问**：一键打开项目的阻碍管理中心

## 文件结构

```
0-辅助/
├── Scripts/
│   ├── createdBlocker.js              # 主要脚本逻辑（已优化）
│   └── 障碍日志创建脚本说明.md          # 本说明文件
└── Templater/
    ├── Function/
    │   └── createdBlocker.md           # Templater调用接口
    └── Notes/
        ├── TP-Project-阻碍.md          # 障碍日志模板
        └── TP-blocker-dashboard.md     # 阻碍管理面板模板
```

## 使用方法

### 启动脚本

1. 在任意项目文件中（路径格式：`2-项目/项目名称/...`）
2. 使用Templater插件执行模板：`0-辅助/Templater/Function/createdBlocker.md`
3. 脚本会弹出选择框，提供两个选项：
   - **新建阻碍**：创建新的障碍日志文件
   - **定位阻碍**：打开项目的阻碍管理面板

### 🆕 新建阻碍流程

选择"新建阻碍"后，脚本会：

1. **自动提取项目信息**：从当前文件路径中提取项目名称
2. **生成文件名**：按照 `blocker-YYYYMMDD-序号.md` 格式生成文件名
3. **输入别名**：弹出对话框让用户输入障碍日志的别名（可选）
4. **应用模板**：使用 `TP-Project-阻碍.md` 模板创建文件
5. **自动填充**：自动设置创建日期、别名和状态
6. **打开文件**：在新标签页中打开创建的文件

### 🎯 定位阻碍流程

选择"定位阻碍"后，脚本会：

1. **检查 Dashboard**：查找项目的 `dashboard.md` 文件
2. **自动创建**：如果不存在，使用 `TP-blocker-dashboard.md` 模板创建
3. **打开面板**：在新标签页中打开阻碍管理面板

## 路径要求

脚本要求当前活动文件的路径符合以下格式：

```
2-项目/[项目名称]/[子目录]/[文件名].md
```

例如：
- `2-项目/个人知识管理系统/2-每日执行/Do-2025-07-01.md`
- `2-项目/仓储管理系统/1-每周规划/Plan-2025-WK27.md`

## 生成的文件

### 障碍日志文件

**文件位置**：`3-过程资产/[项目名称]/阻碍/blocker-YYYYMMDD-XX.md`

**文件命名规则**：
- `blocker-`：固定前缀
- `YYYYMMDD`：当前日期（如：20250718）
- `XX`：当天的序号（01, 02, 03...）

**示例文件名**：
- `blocker-20250718-01.md`（当天第一个障碍日志）
- `blocker-20250718-02.md`（当天第二个障碍日志）

### Dashboard 文件

**文件位置**：`3-过程资产/[项目名称]/阻碍/dashboard.md`

**功能**：
- 项目阻碍管理的中心面板
- 汇总所有障碍日志的入口
- 提供阻碍分析和跟踪功能

## 模板内容

### 障碍日志模板

脚本使用 `0-辅助/Templater/Notes/TP-Project-阻碍.md` 作为模板，并自动：

1. **设置创建日期**：自动填充 `created_Date` 字段
2. **应用别名**：如果用户输入了别名，自动设置 `aliases` 字段
3. **设置状态**：自动将状态设置为"进行中"
4. **保留结构**：保留模板的所有结构和示例内容

### Dashboard 模板

脚本使用 `0-辅助/Templater/Notes/TP-blocker-dashboard.md` 作为 Dashboard 模板，提供：

1. **阻碍概览**：项目阻碍的整体情况
2. **快速导航**：到各个障碍日志的链接
3. **分析工具**：阻碍分类和趋势分析

## 错误处理与用户体验

### 🛡️ 错误处理

脚本包含完善的错误处理机制：

1. **文件路径检查**：如果当前文件不在 `2-项目` 目录下，会显示错误提示
2. **模板文件检查**：如果模板文件不存在，会显示错误提示并停止执行
3. **目录自动创建**：如果目标目录不存在，会自动创建完整的目录结构
4. **文件冲突处理**：自动处理同名文件，通过序号区分
5. **用户取消处理**：用户取消任何操作都不会在文档中插入错误信息

### 🎯 用户体验优化

1. **操作取消友好**：取消任何操作都只显示通知，不会插入多余内容
2. **智能提示**：每个步骤都有清晰的用户提示
3. **自动打开**：创建或定位的文件会自动在新标签页打开
4. **别名可选**：别名输入是可选的，可以直接取消

## 配置要求

### Templater插件配置

1. 启用Templater插件
2. 在设置中配置用户脚本文件夹：`0-辅助/Scripts`
3. 确保模板文件夹包含：`0-辅助/Templater`

### 文件夹结构要求

确保以下文件夹和文件存在：
- `2-项目/`（项目文件夹）
- `3-过程资产/`（过程资产文件夹，脚本会自动创建子目录）
- `0-辅助/Templater/Notes/`（模板文件夹）
- `0-辅助/Templater/Notes/TP-Project-阻碍.md`（障碍日志模板）
- `0-辅助/Templater/Notes/TP-blocker-dashboard.md`（Dashboard 模板）

## 故障排除

### 常见问题

1. **"未找到当前活动文件"**
   - 确保有文件处于活动状态
   - 重新打开文件后再试

2. **"获取项目信息失败"**
   - 检查文件是否在 `2-项目/项目名称/` 目录下
   - 确保路径格式正确

3. **"模板文件不存在"**
   - 检查 `0-辅助/Templater/Notes/TP-Project-阻碍.md` 是否存在
   - 检查 `0-辅助/Templater/Notes/TP-blocker-dashboard.md` 是否存在
   - 确保文件路径和文件名正确

4. **"操作被取消后出现错误信息"**
   - 这是旧版本的问题，新版本已修复
   - 更新到最新版本的脚本

### 🔧 代码优化特性

最新版本的脚本包含以下优化：

1. **模块化设计**：代码分为多个公共函数，便于维护
2. **常量管理**：模板路径和返回信息统一管理
3. **错误处理优化**：所有错误情况都有适当的处理
4. **用户体验提升**：取消操作不会插入错误信息
5. **代码复用**：消除了所有重复代码

## 更新日志

### v2.0（2025-07-18）：重大功能更新
- ✨ **新增选择框功能**：启动时可选择"新建阻碍"或"定位阻碍"
- ✨ **新增定位阻碍功能**：快速打开项目的阻碍管理面板
- ✨ **新增 Dashboard 支持**：自动创建和管理 dashboard.md
- ✨ **新增别名功能**：支持为障碍日志设置自定义别名
- 🔧 **代码全面优化**：模块化设计，消除所有重复代码
- 🔧 **错误处理优化**：用户取消操作不再插入错误信息
- 🔧 **用户体验提升**：更友好的交互和提示信息
- 📁 **目录结构调整**：文件创建在 `阻碍` 子目录下

### v1.0（2025-07-01）：初始版本
- 🎉 初始版本发布
- ✅ 支持基本的障碍日志创建功能
- ✅ 自动提取项目名称和生成文件名
- ✅ 模板应用和自动打开功能

## 技术特性

### 🏗️ 架构设计
- **模块化函数**：`getProjectInfo()`, `readTemplate()`, `openFileInNewTab()` 等
- **常量管理**：`TEMPLATES` 和 `COMMENTS` 对象统一管理配置
- **错误处理**：完善的异常处理和用户反馈机制

### 📊 性能优化
- **代码复用率**：从 ~75% 提升到 ~100%
- **维护性**：模块化设计便于后续功能扩展
- **用户体验**：响应速度快，操作流畅
