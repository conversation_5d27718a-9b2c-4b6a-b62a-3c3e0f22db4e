{"types": {"aliases": "aliases", "cssclasses": "multitext", "tags": "tags", "Project": "text", "startDate": "date", "endDate": "date", "obsidianUIMode": "text", "checkde": "checkbox", "excalidraw-plugin": "text", "excalidraw-export-transparent": "checkbox", "excalidraw-mask": "checkbox", "excalidraw-export-dark": "checkbox", "excalidraw-export-padding": "number", "excalidraw-export-pngscale": "number", "excalidraw-export-embed-scene": "checkbox", "excalidraw-link-prefix": "text", "excalidraw-url-prefix": "text", "excalidraw-link-brackets": "checkbox", "excalidraw-onload-script": "text", "excalidraw-linkbutton-opacity": "number", "excalidraw-default-mode": "text", "excalidraw-font": "text", "excalidraw-font-color": "text", "excalidraw-border-color": "text", "excalidraw-css": "text", "excalidraw-autoexport": "text", "excalidraw-embeddable-theme": "text", "excalidraw-open-md": "checkbox", "TQ_explain": "checkbox", "TQ_extra_instructions": "text", "TQ_short_mode": "checkbox", "TQ_show_backlink": "checkbox", "TQ_show_cancelled_date": "checkbox", "TQ_show_created_date": "checkbox", "TQ_show_depends_on": "checkbox", "TQ_show_done_date": "checkbox", "TQ_show_due_date": "checkbox", "TQ_show_edit_button": "checkbox", "TQ_show_id": "checkbox", "TQ_show_on_completion": "checkbox", "TQ_show_postpone_button": "checkbox", "TQ_show_priority": "checkbox", "TQ_show_recurrence_rule": "checkbox", "TQ_show_scheduled_date": "checkbox", "TQ_show_start_date": "checkbox", "TQ_show_tags": "checkbox", "TQ_show_task_count": "checkbox", "TQ_show_tree": "checkbox", "TQ_show_urgency": "checkbox", "Priority": "number", "Story-points": "number", "storyPoints": "number", "releaseDate": "date", "sprint": "text", "point": "number", "planLink": "text", "upDate": "date", "ctime": "date", "area": "multitext", "createdDate": "date", "updateDate": "date", "created_Date": "date", "update_Date": "date", "weekPlan": "multitext", "dailyDo": "multitext", "weekReplayLink": "multitext", "dailyLink": "multitext", "ReplayLink": "multitext", "reviewLink": "multitext", "mixed": "multitext", "related_task": "multitext", "lastUpdate": "date", "last_Update": "date", "生效日期": "date", "频次": "text", "firstTime": "datetime", "lastTime": "datetime", "reference": "text", "上周复盘": "text", "deadline": "date", "initial_effort": "number", "relation": "multitext", "发生次数": "number", "时间成本": "number", "损耗值": "number", "count": "number", "timeCost": "number", "drain_val": "number"}}