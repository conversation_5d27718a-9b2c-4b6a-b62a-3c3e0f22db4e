---
created_Date: 2025-06-03
aliases:
  - 对陈述、程序、条件行知识缺乏核心认知，导致难以设计对应模板
relation:
  - 整体项目
type: 认知断层
status: 进行中
priority: 1
---
# 1. 基础信息

- 核心问题：不知如何高效的记录陈述、程序、条件性知识
- 发生场景：创建陈述、程序、条件性知识期间
- 关键影响：导致KR1无法顺利推进

# 2. 关键记录

| 时间               | 行动                                                   | 结果                                                                 | 状态   | 发现/洞见                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| ---------------- | ---------------------------------------------------- | ------------------------------------------------------------------ | ---- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2025-06-03 14:00 | 询问DeepSeek寻找可行性方案                                    | 初步明确设计方向                                                           | 验证中  | （1）陈述性知识关注概念的本体：它的定义、属性、构成、分类和基本关系；记录时应该包含核心定义、关键特征/属性、组成部分/结构 (若适用)、分类/类型 (若适用)、核心关系 (重要关联概念)等元素<br>（2）程序性知识是记录操作流程的“行动指南”​，其核心特征是分步操作、可执行、依赖“条件-动作”规则（IF-THEN），模板设计原则：​​ ​清晰化步骤、标准化动作、可视化流程、标注关键细节<br>（3）条件性知识是构建决策树式的“策略卡片”​，其核心特征是情境驱动、规则匹配、依赖元认知判断，模板设计原则：​​ ​明确触发条件、对比选项逻辑、锚定决策依据<br>（4）案例（Case Studies）本身是跨越多类知识的复合型载体，但其核心价值在于抽象出可迁移的经验模式。根据记录目标和后续使用方式，案例可以同时服务于程序性知识、条件性知识，甚至作为陈述性知识的情境补充。模板设计：背景描述（理解情境特征--行业/问题/约束）、​具体操作步骤（提炼可复用的操作方法）、​决策权衡过程（抽象策略选择规则）、结果与反思（形成成功/失败的经验模式） |
| 2025-06-14 16:00 | （1）测试概念（陈述性知识）网络联动逻辑<br>（2）测试程序性知识、条件性知识、陈述性知识的链接顺畅度 | （1）上、下位、相关概念有较大的认知负担，且没有合适的链接载体<br>（2）无合适的方式对程序性知识、条件性知识、陈述性知识进行关联 | 根因确认 | 三大类知识框架不适合个人工作场景下的工作沉淀                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |


# 3. 解决方案

| 日期         | 根因分析                                                            | 行动方案                      | 残留风险 | 后续行动                                                   | 备注   |
| ---------- | --------------------------------------------------------------- | ------------------------- | ---- | ------------------------------------------------------ | ---- |
|            |                                                                 |                           |      |                                                        |      |
